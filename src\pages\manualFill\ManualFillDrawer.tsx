import React, { useState, useEffect, useRef, useMemo } from 'react';
import { YTHForm, YTHDialog, YTHToast, YTHLocalization } from 'yth-ui';
import { message, Button, Spin, Upload } from 'antd';
import { CloudDownloadOutlined } from '@ant-design/icons';
import type { IYTHFormListColumnProps } from 'yth-ui/lib/components/form/list';
import formApi from '@/service/formApi';
import locales from '@/locales';
import { Token, CurrentUser } from '@/Constant';
import { getUuid } from '@/utils/customMethod';
import type { ActionType } from 'yth-ui/lib/components/form/listFilter';
import type { Form } from '@formily/core/esm/models';
import type { ResponseType } from '@/service/envApi';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexInfoById,
  updateManualFillData,
  insertManualFillData,
  downloadManualFilltemplate,
  importDataManualFill,
  ResponsPageType,
} from '@/service/envApi';
import style from './fill.module.less';

const { Dragger } = Upload;

type objType = Record<string, string | number>;
// type columnsTypes = ColumnsType<objType>;
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: 'add' | 'edit' | 'view';
  /** 查看或编辑时 该条数据 */
  dataObj: Record<string, string>;
  closeModal: () => void;
  /** 父级查询条件 */
  queryData: {
    equipCode: objType[];
    monitorType: objType[];
    orgCode: objType[];
  };
  /** 监测设备类型 字典值 */
  dictKey: string;
};

type codeType = {
  code: string | number;
  text: string | number;
  id?: string | number;
};

type formDType = {
  unitType: Array<codeType>;
  monitorType: Array<codeType>;
  equipCode: Array<codeType>;
};

// 基础选项类型
interface OptionItem {
  code: string;
  text: string;
}

// IndexMapItem 类型（假设原始结构包含必要属性）
interface IndexMapItem {
  key: string; // 由 getUuid 生成的唯一键
  // 其他可能的已知属性（根据实际需求添加）
  value?: string;
  label?: string;
}

// 文件URL项的类型
interface FileUrlItem {
  name?: string; // 文件名（可能不存在，需要在代码中处理）
  fileName?: string; // 备用文件名
}

// formD 对象的完整类型
interface FormDType {
  informant: string;
  fillTime: string;
  unitType: [OptionItem];
  monitorType: [OptionItem];
  equipCode?: { code: string; text: string; id: string }[];
  list: IndexMapItem[];
  fileUrl: FileUrlItem[];
  frequencyCd: { code: string; text: string }[];
}

interface NewDataType {
  createdBy?: string;
  createDate?: string;
  unitCode?: string;
  unitName?: string;
  monitorType?: string;
  monitorName?: string;
  equipCode?: string;
  equipName?: string;
  equipId?: string;
  indexMap?: Array<{ [key: string]: unknown }>;
  fileUrl?: string;
  frequency?: string;
}

/**
 * @description  手动填报 新增modal
 * @param param0
 * @returns
 */
const ManualFillDrawer: React.FC<PropsTypes> = ({
  type, // 弹窗的类别 add 新增 view 查看 edit 编辑
  dataObj,
  closeModal = () => {},
  queryData,
  dictKey,
}) => {
  const currentRef: React.Ref<
    | (ActionType & {
        delRow: (name: string, index: number) => void;
        addRows: (name: string, data: object[]) => void;
      })
    | undefined
  > = React.useRef();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);
  const form2: Form = React.useMemo(() => YTHForm.createForm({}), []);
  const [isRefresh, setIsRefresh] = useState<boolean>(false); // 选择所属单位和监测类型重新刷新表头数据
  const [colList, setColList] = useState<IYTHFormListColumnProps[]>([]); // 表头数据list
  const [equipTypeListData, setequipTypeListData] = useState<objType[]>([]); // 设备列表
  const equipTypeListRef: React.MutableRefObject<objType[]> = useRef(equipTypeListData);
  const [createName, setCreateName] = useState<string>(); // 填报人名称

  useEffect(() => {
    equipTypeListRef.current = equipTypeListData;
  }, [equipTypeListData]);

  /**
   * @description 获取表头
   * 需要设备ID和采集频率都存在才能调用
   */
  const getColList: () => Promise<void> = async () => {
    // 获取采集频率
    const formValues: FormDType = form.values;
    const equipId: string | null =
      formValues?.equipCode && formValues.equipCode.length > 0 ? formValues.equipCode[0]?.id : null;

    const frequencyCd: string | null =
      formValues?.frequencyCd && formValues.frequencyCd.length > 0
        ? formValues.frequencyCd[0]?.code
        : null;

    const newCol: IYTHFormListColumnProps[] = [
      {
        title: '监测时间',
        name: 'monitorTime',
        minWidth: 180,
        fixed: 'left',
        edit: type !== 'view',
        required: true,
        componentName: 'DatePicker',
        componentProps: {
          formatter: 'YYYY-MM-DD HH:mm:ss',
          precision: `minute`,
        },
      },
    ];

    // 检查必要参数：设备ID和采集频率都必须存在
    if (!equipId || !frequencyCd) {
      // 缺少必要参数时，只显示时间列
      setColList([...newCol]);
      setIsRefresh(true);
      setIsLoading(false);
      return;
    }

    setIsRefresh(false);
    setIsLoading(true);
    const resData: ResponsPageType = await queryMonitorIndex(equipId, frequencyCd);
    if (resData?.code === 200) {
      if (resData.data instanceof Array) {
        resData.data.forEach((item) => {
          newCol.push({
            title: `${item.name}(${item.measureUnit ? item.measureUnit : '-'})`,
            name: item.code as string,
            minWidth: 150,
            edit: type !== 'view',
            componentName: 'InputNumber',
            componentProps: {},
            // required: true,
          });
        });
      }
    }
    setColList([...newCol]);
    setIsRefresh(true);
    setIsLoading(false);
  };

  // 根据单位和监测类型  查询设备
  const setequipTypeList: (value: Record<string, string>) => Promise<void> = async (value) => {
    if (!value.equipId) {
      form.setValues({ equipCode: [] });
    }
    const plOp: codeType[] = [];
    setIsRefresh(false);
    setIsLoading(true);
    setColList([]);
    const resData: ResponseType = await queryEquipInfoByCompanyId({
      companyId: value?.unitCode || '',
      monitorType: value.monitorType || '',
      type: dictKey,
      monitorOnline: '0',
    });
    if (resData.code === 200 && resData.data instanceof Array && resData.data.length > 0) {
      resData.data.forEach((str) => {
        plOp.push({
          code: str.code,
          text: str.name,
          id: str.id,
        });
      });
    }
    setIsLoading(false);
    setequipTypeListData([...plOp]);
  };

  // 重新设置设备名称选择数据
  const setequipTypeForm: () => void = () => {
    form.query('*').forEach((item) => {
      if (item.path.entire === 'equipCode') {
        item.setComponentProps({
          key: getUuid(16, 17),
          request: async () => {
            return equipTypeListRef.current;
          },
        });
      }
    });
  };

  // 所属单位 或者监测类型改变
  const equipCategoryChange: () => void = () => {
    const { monitorType, unitType = [] } = form.getFormState().values;
    if (unitType[0]?.code) {
      setequipTypeList({ unitCode: unitType[0]?.code, monitorType: monitorType[0]?.code }).then(
        () => {
          setequipTypeForm();
        },
      );
    }
  };

  /**
   * @description 查询数据详情
   */
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    const res: ResponseType = await queryMonitorIndexInfoById({ id: dataObj.id as string });
    if (res && res?.code && res?.code === 200) {
      const newData: NewDataType = (res.data as NewDataType) || {};
      const formD: FormDType = {
        informant: '',
        fillTime: '',
        unitType: [
          {
            code: '',
            text: '',
          },
        ],
        monitorType: [
          {
            code: '',
            text: '',
          },
        ],
        equipCode: [
          {
            code: '',
            text: '',
            id: '',
          },
        ],
        list: [],
        fileUrl: [],
        frequencyCd: [
          {
            code: '',
            text: '',
          },
        ],
      };
      formD.informant = newData.createdBy ?? '';
      formD.fillTime = newData.createDate ?? '';
      formD.unitType = [
        {
          code: newData.unitCode ?? '',
          text: newData.unitName ?? '',
        },
      ];
      formD.frequencyCd = [
        {
          code: newData.frequency ?? '',
          text: '',
        },
      ];
      formD.monitorType = [
        {
          code: newData.monitorType ?? '',
          text: newData.monitorName ?? '',
        },
      ];
      formD.equipCode = [
        {
          code: newData.equipCode ?? '',
          text: newData.equipName ?? '',
          id: newData.equipId ?? '',
        },
      ];
      formD.list = newData.indexMap?.map((item) => {
        return { ...item, key: getUuid(16, 17) };
      });
      formD.fileUrl = JSON.parse(newData.fileUrl) || [];
      const fileName: string | null = formD.fileUrl.length > 0 ? formD.fileUrl[0]?.name : null;
      if (!fileName && formD.fileUrl.length > 0) {
        formD.fileUrl[0].name = formD.fileUrl[0]?.fileName;
      }
      setCreateName((res.data?.createdBy as string) ?? '');
      form.setValues(formD);
      form2.setValues(formD);
      setIsLoading(false);
      getColList();
    }
  };

  /**
   * @description 取消
   */
  const cancel: () => void = () => {
    form.reset();
    closeModal();
    setequipTypeListData([]);
  };

  /**
   * @description 新增
   */
  const submitAddData: (data: Record<string, unknown>) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: ResponseType = await insertManualFillData(data);
    if (res && res?.code && res?.code === 200) {
      message.success('新增数据成功');
      closeModal();
      setequipTypeListData([]);
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  /**
   * @description 更新
   */
  const submitEditData: (data: Record<string, unknown>) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: ResponseType = await updateManualFillData({ ...data, id: dataObj?.id });
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
      setequipTypeListData([]);
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  /**
   * @description 保存
   */
  const save: () => void = () => {
    form.validate().then(() => {
      form2.validate().then(() => {
        if (!form.values?.monitorType[0]?.code) {
          message.info('请选择监测类型');
          return;
        }
        const listObj: IndexMapItem[] = JSON.parse(JSON.stringify(form2.values.list));
        const submitData: Record<string, unknown> = {
          equipName: form.values.equipCode[0]?.text ?? '',
          equipCode: form.values.equipCode[0]?.code ?? '',
          equipId: form.values.equipCode[0]?.id ?? '',
          unitCode: form.values.unitType[0]?.code ?? '',
          unitName: form.values.unitType[0]?.text ?? '',
          monitorType: form.values.monitorType[0]?.code ?? '',
          frequency: (form.values.frequencyCd && form.values.frequencyCd[0]?.code) || '',
          indexMap: listObj,
          type: dictKey,
          createdBy: createName || CurrentUser().realName,
          fileUrl: JSON.stringify(form.values.fileUrl),
        };
        if (type === 'add') {
          submitAddData(submitData);
        } else if (type === 'edit') {
          submitEditData(submitData);
        }
      });
    });
  };

  const deleteTemplateDialog: (index: number) => void = (index) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        currentRef?.current?.delRow('list', index + 1);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  // 下载导入模板
  const downloadTemplate: () => Promise<void> = async () => {
    interface ParamsType {
      companyName?: string;
      orgCode?: string;
      monitorType?: string;
      equipCode?: string;
      equipName?: string;
      equipId?: string;
      frequency?: string;
    }
    const formQuery: FormDType = form.getFormState().values;
    if (!formQuery.equipCode || formQuery.equipCode.length === 0 || !formQuery.equipCode[0]?.code) {
      message.info('请选择设备名称');
      return;
    }
    if (!formQuery.frequencyCd || formQuery.frequencyCd.length === 0) {
      message.info('请选择采集频率');
      return;
    }
    const params: ParamsType = {};
    if (formQuery.unitType && formQuery.unitType.length > 0) {
      params.companyName = formQuery.unitType[0].text;
    }
    if (formQuery.unitType && formQuery.unitType.length > 0) {
      params.orgCode = formQuery.unitType[0].code;
    }
    if (formQuery.monitorType && formQuery.monitorType.length > 0) {
      params.monitorType = formQuery.monitorType[0].code;
    }
    if (formQuery.equipCode && formQuery.equipCode.length > 0) {
      params.equipCode = formQuery.equipCode[0].code;
    }
    if (formQuery.equipCode && formQuery.equipCode.length > 0) {
      params.equipName = formQuery.equipCode[0].text;
    }
    if (formQuery.equipCode && formQuery.equipCode.length > 0) {
      params.equipId = formQuery.equipCode[0].id;
    }
    if (formQuery.frequencyCd && formQuery.frequencyCd.length > 0) {
      params.frequency = formQuery.frequencyCd?.[0]?.code;
    }
    const resData: BlobPart = await downloadManualFilltemplate({ ...params });
    const href: string = window.URL.createObjectURL(
      new Blob([resData], { type: 'application/vnd.ms-excel;charset=utf-8' }),
    );
    const link: HTMLAnchorElement = document.createElement('a');
    link.href = href;
    link.download = dictKey === 'A22A08A06' ? '环境质量监测导入模版' : '污染源监测导入模版';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(href);
  };

  /**
   * 上传模板
   */
  const uploadTemplate: () => Promise<void> = async () => {
    // 创建一个新的 FormData 对象
    const files: FormData = new FormData();
    YTHDialog.show({
      type: 'confirm',
      content: (
        <Dragger
          multiple={false}
          action=""
          onChange={(info) => files.append('file', info.file.originFileObj)}
          accept="xlsx,xls"
        >
          <p className={style['ant-upload-drag-icon']}>
            <CloudDownloadOutlined />
          </p>
          <p className={style['ant-upload-text']}>点击选取或者将文件拖拽至此区域</p>
          <p className={style['ant-upload-hint']}>
            当前上传只支持xlsx、xls格式文件,一次上传一个文件，不支持多文件上传
          </p>
        </Dragger>
      ),
      onConfirm: async () => {
        const formQuery: FormDType = form.getFormState().values;

        if (formQuery.unitType && formQuery.unitType.length > 0) {
          files.append('orgCode', formQuery.unitType[0].code);
        }
        if (formQuery.monitorType && formQuery.monitorType.length > 0) {
          files.append('monitorType', formQuery.monitorType[0].code);
        }
        if (formQuery.equipCode && formQuery.equipCode.length > 0) {
          files.append('equipId', formQuery.equipCode[0].id);
        }
        // 上传
        const res: ResponseType = await importDataManualFill(files);
        if (res.code === 200) {
          currentRef?.current.addRows('list', Array.isArray(res.data) ? res.data : []);
          YTHToast.show({
            type: 'success',
            messageText: '上传成功',
            p_props: { duration: 10 },
            m_props: { duration: 100 },
          });
        } else {
          YTHToast.show({
            type: 'error',
            messageText: res.msg,
            p_props: { duration: 10 },
            m_props: { duration: 100 },
          });
        }
      },
      p_props: {
        title: '提示',
        width: 600,
      },
    });
  };

  const YTHFormList: JSX.Element = useMemo(() => {
    return (
      <div>
        {!isRefresh ? (
          <YTHForm form={form2} col={2}>
            <YTHForm.List
              title="监测指标"
              columns={[
                {
                  title: '监测时间',
                  name: 'monitorTime',
                  minWidth: 180,
                  fixed: 'left',
                  edit: type !== 'view',
                  componentName: 'DatePicker',
                  componentProps: {
                    formatter: 'YYYY-MM-DD HH:mm:ss',
                    precision: `minute`,
                  },
                },
              ]}
              name="list2"
              rowOperations={[
                {
                  key: `list_key_${String(new Date())}`,
                  title: '删除',
                  type: 'danger',
                  disabled: () => type === 'view',
                  operation: (index, row) => {
                    if (row && row.id && row.id !== '') {
                      deleteTemplateDialog(index);
                    } else {
                      currentRef?.current.delRow('list', index + 1);
                    }
                  },
                },
              ]}
              extra={[
                {
                  key: 'addData',
                  title: '添加数据',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    if (colList.length === 0) {
                      return message.info('缺少监测指标或设备名称');
                    }
                    currentRef?.current.addRows('list', [{}]);
                    return '';
                  },
                },
                {
                  key: 'uploadTemplate',
                  title: '导入数据',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    if (colList.length === 0) {
                      return message.info('缺少监测指标或设备名称');
                    }
                    uploadTemplate();
                    return '';
                  },
                },
                {
                  key: 'downloadTemplate',
                  title: '下载模板',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    downloadTemplate();
                  },
                },
              ]}
              actionRef={currentRef}
            />
          </YTHForm>
        ) : (
          <YTHForm form={form2} col={2}>
            <YTHForm.List
              title="监测指标"
              columns={colList}
              name="list"
              rowOperations={[
                {
                  key: `list_key_${String(new Date())}`,
                  title: '删除',
                  type: 'danger',
                  disabled: () => type === 'view',
                  operation: (index, row) => {
                    if (row && row.id && row.id !== '') {
                      return deleteTemplateDialog(index);
                    }
                    currentRef?.current.delRow('list', index + 1);
                    return '';
                  },
                },
              ]}
              extra={[
                {
                  key: 'addData',
                  title: '添加数据',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    if (colList.length === 0) {
                      return message.info('缺少监测指标或设备名称');
                    }
                    currentRef?.current.addRows('list', [{}]);
                    return '';
                  },
                },
                {
                  key: 'uploadTemplate',
                  title: '导入数据',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    if (colList.length === 0) {
                      return message.info('缺少监测指标或设备名称');
                    }
                    uploadTemplate();
                    return '';
                  },
                },
                {
                  key: 'downloadTemplate',
                  title: '下载模板',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    downloadTemplate();
                  },
                },
              ]}
              actionRef={currentRef}
            />
          </YTHForm>
        )}
      </div>
    );
  }, [isRefresh, colList]);

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().then(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
  }, [type]);

  useEffect(() => {
    if (type === 'edit' || type === 'view') return;
    const formD: formDType = {
      unitType: [],
      monitorType: [],
      equipCode: [],
    };
    const { equipCode = [], monitorType = [], orgCode = [] } = queryData;
    formD.unitType = [
      {
        code: orgCode[0]?.code ?? '',
        text: orgCode[0]?.text ?? '',
      },
    ];
    formD.monitorType = [
      {
        code: monitorType[0]?.code ?? '',
        text: monitorType[0]?.text ?? '',
      },
    ];
    formD.equipCode = [
      {
        code: equipCode[0]?.code ?? '',
        text: equipCode[0]?.text ?? '',
        id: equipCode[0]?.id ?? '',
      },
    ];
    form.setValues(formD);
    if (orgCode.length > 0) {
      setequipTypeList({
        unitCode: (orgCode[0]?.code as string) || '',
        monitorType: (monitorType[0]?.code as string) || '',
        equipId: (equipCode[0]?.id as string) || '',
      }).then(() => {
        setTimeout(() => {
          setequipTypeForm();
        }, 500);
      });
    }
  }, [queryData, type]);

  useEffect(() => {
    if (dataObj?.id) {
      setequipTypeList(dataObj).then(() => {
        setTimeout(() => {
          setequipTypeForm();
        }, 500);
      });
    }
    return () => {
      setColList([]);
    };
  }, [dataObj]);

  return (
    <div>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="id"
              labelType={1}
              required={false}
              display="hidden"
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
            <YTHForm.Item
              name="unitType"
              title="所属单位"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { data } = await queryUnitInfoByType(dictKey);
                  const plOp: codeType[] = [];
                  if (data instanceof Array) {
                    data.forEach((item) => {
                      plOp.push({
                        code: item.companyId,
                        text: item.supplyUnit,
                      });
                    });
                  }
                  return plOp;
                },
                onChange: equipCategoryChange,
                multiple: false,
                disabled: type === 'view',
                p_props: {
                  placeholder: '请输入',
                },
              }}
            />
            <YTHForm.Item
              name="monitorType"
              title="监测类型"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { list } = await formApi.getDictionary({
                    condition: {
                      fatherCode: dictKey,
                    },
                    currentPage: 0,
                    pageSize: 0,
                  });
                  const plOp: codeType[] = [];
                  if (list instanceof Array) {
                    list.forEach((item) => {
                      if (dictKey === 'A22A08A07') {
                        if (item.code !== 'A22A08A07A05') {
                          plOp.push({
                            code: item.code,
                            text: item.text,
                          });
                        }
                      } else {
                        plOp.push({
                          code: item.code,
                          text: item.text,
                        });
                      }
                    });
                  }
                  return plOp;
                },
                onChange: equipCategoryChange,
                disabled: type === 'view',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
              }}
            />
            <YTHForm.Item
              name="equipCode"
              title="设备名称"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                disabled: type === 'view',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
                onChange: getColList,
              }}
            />
            <YTHForm.Item
              name="fileUrl"
              title="附件上传"
              componentName="Upload"
              // mergeRow={1}
              labelType={1}
              componentProps={{
                disabled: type === 'view',
                placeholder: '请输入',
                listType: `yth-card`,
                // maxCount: 1,
                name: 'file',
                action: '/gw/form-api/file/upload',
                headers: {
                  authorization: Token(),
                },
                online: '/preview/onlinePreview',
                data: {
                  formCode: 'form_5964',
                },
              }}
            />
            <YTHForm.Item
              name="frequencyCd"
              title="采集频率"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { list } = await formApi.getDictionary({
                    condition: {
                      fatherCode: 'A23A04',
                    },
                    currentPage: 0,
                    pageSize: 0,
                  });
                  const plOp: object[] = [];
                  list.forEach((item) => {
                    plOp.push({
                      text: item.text,
                      code: item.remark,
                    });
                  });
                  return plOp;
                },
                multiple: false,
                disabled: type === 'view',
                p_props: {
                  placeholder: '请选择',
                },
                onChange: getColList,
              }}
            />
            <YTHForm.Item
              name="informant"
              title="填报人"
              labelType={1}
              required
              display={type === 'view' ? 'visible' : 'hidden'}
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
            <YTHForm.Item
              name="fillTime"
              title="填报时间"
              labelType={1}
              display={type === 'view' ? 'visible' : 'hidden'}
              required
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
          </YTHForm>
        )}

        {YTHFormList}
        <div className={style['drawer-filter-operation']}>
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} className={style['search-btn']} type="primary">
              保存
            </Button>
          )}
        </div>
      </Spin>
    </div>
  );
};
export default YTHLocalization.withLocal(ManualFillDrawer, locales, YTHLocalization.getLanguage());
