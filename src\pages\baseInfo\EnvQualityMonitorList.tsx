import React, { useState, useRef, Key } from 'react';
import { YTHList, YTHDialog, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import locales from '@/locales';
import {
  deleteEnvQualityById,
  queryEnvQualityMonitorList,
  queryEquipInfoByCompanyId,
} from '@/service/envApi';
import type { ResponseType, ResponsPageType } from '@/service/envApi';
import formApi from '@/service/formApi';
import { queryUnitTreeData } from '@/service/baseModuleApi';
import { getUuid } from '@/utils/customMethod';
import { formatTree } from '@/utils/format';
import MonitorDrawer from './MonitorDrawer';
import style from './baseInfo.module.less';

type filterType = {
  type?: string;
  companyId?: string;
  monitorType?: string;
  code?: string;
  state?: string;
  equipType?: string;
};

/**
 * 监测设备类型字典值
 */
const dictKey: string = 'A22A08A06';

/**
 * @description 环境空气质量 基础信息管理
 * @returns
 */
const EnvQualityOnLineMonitorList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<string>(''); // 是否是新增模式
  const [dataObj, setDataObj] = useState<{ [key: string]: React.Key }>({}); // 查看或编辑行数据
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  // 根据所属单位和检测类型 查询设备数据
  const getAccessList: () => Promise<void> = async () => {
    const monitorItem: Record<string, string>[] = aa.getQuery().monitorType || [];
    const companyItem: Record<string, string>[] = aa.getQuery().orgCode || [];
    aa.setQuery({ monitorType: monitorItem, orgCode: companyItem, equipCode: [] });
    if (companyItem) {
      aa.getQueryForm()
        .query('*')
        .forEach(
          (item: {
            path: { entire: string };
            setComponentProps: (props: unknown) => void;
            setValue: (value: unknown) => void;
          }) => {
            if (item.path.entire === 'equipCode') {
              item.setValue([{ text: null, code: null, id: null }]);
              item.setComponentProps({
                key: getUuid(16, 17),
                request: async () => {
                  const resData: ResponseType = await queryEquipInfoByCompanyId({
                    companyId: companyItem[0]?.id,
                    monitorType: monitorItem[0]?.code,
                    type: dictKey,
                  });
                  if (resData?.data instanceof Array && resData?.data?.length) {
                    const result: { text: string; code: string }[] = resData.data.map((resItem) => {
                      return { text: resItem.name, code: resItem.code };
                    });
                    return result ?? [];
                  }
                  return [];
                },
                searchable: true,
              });
            }
          },
        );
    }
  };

  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'orgCode',
      title: '所属单位',
      width: 180,
      query: true,
      display: true,
      componentName: 'PickData',
      componentProps: {
        requestData: async () => {
          try {
            return formatTree(await queryUnitTreeData());
          } catch {
            return [];
          }
        },
        onChange: getAccessList,
        multiple: false,
        p_props: {
          placeholder: '请输入',
        },
      },
      render: (_v, record) => {
        return record.supplyUnit || '-';
      },
    },
    {
      dataIndex: 'monitorType',
      title: '监测类型',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          placeholder: '请选择',
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: dictKey,
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: { text: string; code: string }[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.code,
            });
          });
          return plOp;
        },
        onChange: getAccessList,
      },
      render: (_v, record) => {
        return record.monitorTypeText || '-';
      },
    },
    {
      dataIndex: 'equipCode',
      title: '设备名称',
      width: 180,
      query: true,
      display: false,
      componentName: 'Selector',
      componentProps: {
        multiple: false,
        p_props: {
          placeholder: '请选择',
        },
      },
    },
    {
      dataIndex: 'name',
      title: '设备名称',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'equipType',
      title: '设备类型',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A05',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: { text: string; code: string }[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.code,
            });
          });
          return plOp;
        },
        p_props: {
          changeOnSelect: true,
          placeholder: '请输入',
        },
      },
      render: (_v, record) => {
        return record.equipTypeText || '-';
      },
    },
    {
      dataIndex: 'code',
      title: '设备编码',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'stateText',
      title: '设施运行状态',
      width: 180,
      componentName: 'Selector',
      query: true,
      display: true,
      componentProps: {
        p_props: {
          allowClear: true,
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A03',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: { text: string; code: string }[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.code,
            });
          });
          return plOp;
        },
      },
    },
    {
      dataIndex: 'description',
      title: '监测对象',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'location',
      title: '设备位置',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'createDate',
      title: '创建时间',
      width: 180,
      query: false,
      display: true,
    },
  ];

  const setModalTitle: () => string = () => {
    let title: string = '';
    if (modalType === 'add') {
      title = '新增';
    } else if (modalType === 'view') {
      title = '查看';
    } else if (modalType === 'edit') {
      title = '编辑';
    }
    return title;
  };

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
    Modal.destroyAll();
  };

  const confirmDelete: (row: { id: string }) => Promise<void> = async (row) => {
    const res: ResponseType = await deleteEnvQualityById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      aa.reload({});
    } else {
      message.error('删除数据出错');
    }
  };
  const deleteTemplateDialog: (row: { id: string }) => void = (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  const handleFilter: (f: Record<string, Record<string, string>[]>) => filterType = (f) => {
    const filter: filterType = {};
    filter.type = dictKey;
    if (f.orgCode && f.orgCode.length > 0) {
      filter.companyId = f.orgCode[0]?.id;
    }
    if (f.monitorType && f.monitorType.length > 0) {
      filter.monitorType = f.monitorType[0].code;
    }
    if (f.equipCode && f.equipCode.length > 0) {
      filter.code = f.equipCode[0].code;
    }
    if (f.stateText && f.stateText.length > 0) {
      filter.state = f.stateText[0].code;
    }
    if (f.equipType && f.equipType.length > 0) {
      filter.equipType = f.equipType[0].code;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="envQualityOnLineMonitorList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ResponsPageType = await queryEnvQualityMonitorList({
            descs: [''],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            const dataWithSerialNo: Record<string, unknown>[] = resData.data.map((item, index) => ({
              ...item,
              serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
            }));
            /*
             */
            return {
              data: dataWithSerialNo,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={200}
        rowOperation={(row: { id: string; [key: string]: Key }) => {
          return [
            {
              element: (
                <div className={style['gas-leak-monitor-row-operator']}>
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('edit');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      编辑
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row);
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="60%"
        title={setModalTitle()}
        footer={null}
        visible={editMenuVisiable}
        onCancel={closeModal}
        destroyOnClose
        maskClosable={false}
      >
        <MonitorDrawer
          type={modalType}
          closeModal={closeModal}
          dataObj={dataObj}
          dictKey={dictKey}
        />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  EnvQualityOnLineMonitorList,
  locales,
  YTHLocalization.getLanguage(),
);
