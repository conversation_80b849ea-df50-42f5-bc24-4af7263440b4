import React, { useState, useEffect, useRef } from 'react';
import { YTHForm, YTHList } from 'yth-ui';
import { message, <PERSON><PERSON>, Spin } from 'antd';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import moment from 'moment';
import traceApi from '@/service/traceApi';
import type { ApiResponse, TraceRecord, AlarmInfo } from '@/service/traceApi';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj: TraceRecord;
  closeModal: () => void;
};

/**
 * @description 溯源记录 查看、处理弹窗
 * @returns
 */
const TraceabilityModal: React.FC<PropsTypes> = ({ dataObj, closeModal = () => {} }) => {
  const [isLoading] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // YTHList 相关状态
  const listActionRef: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();

  // 报警信息列表列配置
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'supplyUnit',
      title: '所属单位',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'equipName',
      title: '设备名称',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'equipCode',
      title: '设备编号',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'indexName',
      title: '监测指标名称',
      width: 180,
      display: true,
    },
    {
      dataIndex: 'currVal',
      title: '当前值',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'measureUnit',
      title: '计量单位',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'alarmTime',
      title: '报警时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      dataIndex: 'alarmLevelText',
      title: '报警级别',
      width: 120,
      display: true,
    },
  ];

  useEffect(() => {
    if (dataObj && dataObj.id && dataObj.id !== '') {
      form.setValues({
        ...dataObj,
        id: dataObj.id,
        handleTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      });
    } else {
      message.error('数据出错').then(() => {
        closeModal();
      });
    }
  }, [dataObj, form, closeModal]);

  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        <YTHForm form={form} col={2}>
          {/* <YTHForm.Item
            name="alarmId"
            title="报警ID"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          /> */}

          <YTHForm.Item
            name="pollutionTypeText"
            title="污染类型"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingCode"
            title="溯源指标"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="pollutantConcentration"
            title="相关报警值"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="highRiskEnterpriseName"
            title="高风险企业名称"
            labelType={1}
            mergeRow={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="lowRiskEnterpriseName"
            title="低风险企业名称"
            labelType={1}
            mergeRow={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="tracingTime"
            title="溯源时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              disabled: true,
              placeholder: '',
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
            }}
          />
        </YTHForm>

        {/* 底部添加YTHList组件 */}
        <div
          style={{ marginTop: '20px', display: dataObj.highRiskEnterpriseId ? 'block' : 'none' }}
        >
          <span style={{ marginLeft: '10px', fontSize: '14px' }}>报警记录</span>
          <YTHList
            defaultQuery={{}}
            code="alarmInfoModalList"
            action={listAction}
            actionRef={listActionRef}
            showRowSelection={false}
            operation={[]}
            listKey="id"
            extraOperation={[]}
            request={async (filter, pagination) => {
              try {
                const resData: ApiResponse<AlarmInfo[]> = await traceApi.queryAlarmInfoList({
                  aescs: [],
                  descs: [],
                  condition: { alarmIds: dataObj?.alarmId }, // 使用传入的报警ID进行查询
                  currentPage: pagination.current,
                  pageSize: pagination.pageSize,
                });

                if (resData.code && resData.code === 200) {
                  resData.data.forEach((_item, index) => {
                    (resData.data[index] as AlarmInfo & { serialNo?: number }).serialNo =
                      (pagination.current - 1) * pagination.pageSize + index + 1;
                  });
                  return {
                    data: resData.data,
                    total: resData.total,
                    success: true,
                  };
                }
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              } catch {
                // 查询报警信息失败，返回空数据
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              }
            }}
            columns={columns}
          />
        </div>

        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Button onClick={cancel}>取消</Button>
        </div>
      </Spin>
    </div>
  );
};

export default TraceabilityModal;
