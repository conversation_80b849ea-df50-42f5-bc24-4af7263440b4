import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import { queryAlarmPageList, ResponsPageType } from '@/service/envApi';
import formApi from '@/service/formApi';
import locales from '@/locales';
import commonstyle from './common.module.less';
import ElarmModal from './AlarmModalNew';

type filterType = {
  type?: string;
  equipName?: string;
  supplyUnit?: string;
  description?: string;
  indexName?: string;
  frequency?: { code: string; text: string }[];
  equipState?: { code: string; text: string }[];
};

type filterTypeData = {
  equipName?: string;
  supplyUnit?: string;
  description?: string;
  indexName?: string;
  frequency?: string;
  equipState?: string;
  type?: string;
};

/**
 * @description 污染源报警
 * @returns
 */
const PolluteAlarmList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<'handle' | 'view'>('view'); // view handle 查看还是处置
  const [dataObj, setDataObj] = useState<Record<string, string | number>>();
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'supplyUnit',
      title: '所属单位',
      width: 180,
      query: true,
      display: true,
      fixed: 'left',
    },
    {
      dataIndex: 'equipName',
      title: '设备名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'equipCode',
      title: '设备编号',
      width: 180,
      query: false,
      display: true,
    },
    // {
    //   dataIndex: 'equipState',
    //   title: '设备状态',
    //   width: 180,
    //   query: true,
    //   display: true,
    //   componentName: 'Selector',
    //   componentProps: {
    //     p_props: {
    //       allowClear: true,
    //     },
    //     request: () => {
    //       return [
    //         { code: '0', text: '离线' },
    //         { code: '1', text: '在线' },
    //       ];
    //     },
    //   },
    //   render: (value) => {
    //     if (value === '0') {
    //       return <div>离线</div>;
    //     }
    //     if (value === '1') {
    //       return <div>在线</div>;
    //     }
    //     return <div> - </div>;
    //   },
    // },
    {
      dataIndex: 'equipState',
      title: '是否在线监测',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '请选择',
        },
        request: () => {
          return [
            { code: '1', text: '是' },
            { code: '0', text: '否' },
          ];
        },
      },
      render: (value) => {
        if (value === '0') {
          return <div>否</div>;
        }
        if (value === '1') {
          return <div>是</div>;
        }
        return <div> - </div>;
      },
    },
    {
      dataIndex: 'description',
      title: '监测对象',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'frequency',
      title: '采集频率',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          placeholder: '请选择',
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A23A04',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: object[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.remark,
            });
          });
          return plOp;
        },
        multiple: false,
      },
      render: (_v, record) => {
        return record.frequencyText || '-';
      },
    },

    {
      dataIndex: 'indexName',
      title: '监测指标名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'measureUnit',
      title: '计量单位',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'currVal',
      title: '当前值',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'firstLevelMax',
      title: '一级阈值上限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'firstLevelMin',
      title: '一级阈值下限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'secondLevelMax',
      title: '二级阈值上限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'secondLevelMin',
      title: '二级阈值下限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'alarmTime',
      title: '报警开始时间',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'alarmLevelText',
      title: '报警级别',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'maxRealtimeValLongitude',
      title: '最大实时值的经度',
      width: 180,
      query: false,
      display: false,
    },
    {
      dataIndex: 'maxRealtimeValLatitude',
      title: '最大实时值的纬度',
      width: 180,
      query: false,
      display: false,
    },
    {
      dataIndex: 'disposeState',
      title: '处置情况',
      width: 180,
      query: false,
      display: true,
      render: (value) => {
        if (value === '0' || value === 0) {
          return <span style={{ color: 'red' }}>未处置</span>;
        }
        if (value === '1' || value === 1) {
          return <span style={{ color: 'green' }}>已处置</span>;
        }
        return <div> - </div>;
      },
    },
    {
      dataIndex: 'disposeTime',
      title: '处置时间',
      width: 180,
      query: false,
      display: true,
    },
  ];

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
    Modal.destroyAll();
  };

  const handleFilter: (f: filterType) => filterTypeData = (f) => {
    const filter: filterTypeData = {
      type: 'A22A08A07',
      equipName: f.equipName || '',
      supplyUnit: f.supplyUnit || '',
      description: f.description || '',
      indexName: f.indexName || '',
      frequency: f.frequency && f.frequency.length > 0 ? f.frequency[0]?.code : '',
      equipState: f.equipState && f.equipState.length > 0 ? f.equipState[0].code : undefined,
    };
    return filter;
  };

  return (
    <div className={commonstyle['gas-leak-monitor-template-container']}>
      <YTHList
        defaultQuery={{}}
        code="polluteAlarmList"
        action={aa}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ResponsPageType = await queryAlarmPageList({
            aescs: [],
            descs: [],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            /*
             */
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div className={commonstyle['gas-leak-monitor-row-operator']}>
                  <Space size="middle">
                    <Button
                      type="link"
                      size="small"
                      className={commonstyle['p-btn-detail']}
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>
                    {row.disposeState && row.disposeState === '0' && (
                      <Button
                        type="link"
                        size="small"
                        className={commonstyle['p-btn-detail']}
                        onClick={() => {
                          setModalType('handle');
                          setDataObj(row);
                          setEditMenuVisiable(true);
                        }}
                      >
                        处置
                      </Button>
                    )}
                    {!row.disposeState ||
                      (row.disposeState !== '0' && (
                        <Button type="link" size="small" disabled>
                          处置
                        </Button>
                      ))}
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title={modalType === 'view' ? '污染源报警记录' : '污染源处置信息'}
        width="70%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={editMenuVisiable}
        key="right-drawer"
      >
        <ElarmModal modalType={modalType} closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(PolluteAlarmList, locales, YTHLocalization.getLanguage());
