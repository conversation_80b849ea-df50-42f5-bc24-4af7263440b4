import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  aescs?: string[];
  descs?: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 溯源记录查询参数接口
 */
export interface TraceRecordQueryParams {
  /** 污染类型 */
  pollutionType?: string;
  /** 溯源指标 */
  tracingCode?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/**
 * 溯源记录数据接口
 */
export interface TraceRecord {
  /** 主键 */
  id: string;
  /** 报警id */
  alarmId: string;
  /** 高风险企业id */
  highRiskEnterpriseId: string;
  /** 高风险企业名称 */
  highRiskEnterpriseName: string;
  /** 低风险企业id */
  lowRiskEnterpriseId: string;
  /** 低风险企业名称 */
  lowRiskEnterpriseName: string;
  /** 相关报警值 */
  pollutantConcentration: number;
  /** 污染类型 */
  pollutionType: string;
  /** 溯源指标 */
  tracingCode: string;
  /** 溯源时间 */
  tracingTime: string;
  /** 污染类型文本 */
  pollutionTypeText?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 环境管理报警信息数据接口
 */
export interface AlarmInfo {
  /** 主键 */
  id: string;
  /** 报警级别 */
  alarmLevel: string;
  /** 报警时间 */
  alarmTime: string;
  /** 当前值 */
  currVal: number;
  /** 监测对象 */
  description: string;
  /** 处置内容 */
  disposeContent: string;
  /** 处置人 */
  disposePersonnel: string;
  /** 处置状态 1-已处置 */
  disposeState: string;
  /** 处置时间 */
  disposeTime: string;
  /** 设备编号 */
  equipCode: string;
  /** 设备名称 */
  equipName: string;
  /** 监测设备状态 0-离线 1-在线 */
  equipState: string;
  /** 一级阈值上限 */
  firstLevelMax: number;
  /** 一级阈值下限 */
  firstLevelMin: number;
  /** 采集频率 */
  frequency: string;
  /** 监测指标编码 */
  indexCode: string;
  /** 监测指标名称 */
  indexName: string;
  /** 计量单位 */
  measureUnit: string;
  /** 单位编码 */
  orgCode: string;
  /** 单位名称 */
  orgNm: string;
  /** 二级阈值上限 */
  secondLevelMax: number;
  /** 二级阈值下限 */
  secondLevelMin: number;
  /** 所属单位 */
  supplyUnit: string;
  /** 设备监测类型 */
  type: string;
}

/**
 * 溯源 API 接口定义
 */
interface TraceApiInterface {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (
    data: PageQueryParams<TraceRecordQueryParams>,
  ) => Promise<ApiResponse<TraceRecord[]>>;
  /** 查询环境管理报警信息列表 */
  queryAlarmInfoList: (
    data: PageQueryParams<{ alarmIds: string }>,
  ) => Promise<ApiResponse<AlarmInfo[]>>;
}

/**
 * 溯源模块 API
 */
const traceApi: TraceApiInterface = {
  /** 查询溯源记录列表 */
  queryTraceRecordList: (data) => {
    return envRequest.post('/pollutionTracing/page', { data });
  },

  /** 查询环境管理报警信息列表 */
  queryAlarmInfoList: (data) => {
    return envRequest.post('/envAlarm/page', { data });
  },
};

export default traceApi;
