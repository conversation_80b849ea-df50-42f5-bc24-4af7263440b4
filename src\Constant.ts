import { isEmpty, YTHLocalization } from 'yth-ui';

export const LANGUAGE_STORE_KEY: string = '$_language';

export const CurrentUser: () => Record<string, string> = () => {
  // return JSON.parse(window.sessionStorage.getItem('$_user') || '{}');
  return {
    id: '86e471bb640a3727e1a5bf762234bbd1',
    username: '**********',
    realName: '安宁化工园区单位管理员',
    accountId: '700b9fb43efff78e2f04ade69844bf64',
    unitId: '700b9fb43efff78e2f04ade69844bf64',
    tenantId: 1,
    unitCode: '**********',
    unitName: '云南省安宁产业园区管理委员会',
    unitType: '-1',
  };
};

export const Token: () => string = () => {
  // return window.sessionStorage.getItem('$_token') || '';
  return 'bearer fa5d8e78-63bb-41ba-80dc-695e1ba6ac39';
};

export const Setting: () => Record<string, string> = () => {
  return JSON.parse(window.localStorage.getItem('yth_form_config_setting') || '{}');
};

/**
 * 配置request请求时的默认参数
 */
export const ConstHeaders: () => Headers = () => {
  const { affinityHost = '' } = Setting();
  const headers: Headers = new Headers();
  headers.append('Content-Language', YTHLocalization.getLanguage());
  if (Token()) {
    headers.append('Authorization', Token() || '');
  }
  if (!isEmpty(affinityHost)) {
    headers.append('affinity_host', affinityHost);
  }
  return headers;
};
