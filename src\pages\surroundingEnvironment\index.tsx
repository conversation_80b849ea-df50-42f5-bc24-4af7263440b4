import React, { useState, useRef, useMemo } from 'react';
import { YTHList, YTHLocalization, YTHDialog } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message } from 'antd';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type {
  ApiResponse,
  SurroundingEnvRecord,
  SurroundingEnvQueryParams,
} from '@/service/surroundingEnvApi';
import locales from '@/locales';
import formApi from '@/service/formApi';
import SurroundingEnvModal from './components/SurroundingEnvModal';

// 常量定义
const SUCCESS_CODE: number = 200;

type FilterType = {
  areaName: string;
  category: { code: string; text: string }[];
};

/**
 * @description 园区周边环境管理页面
 * @returns
 */
const SurroundingEnvironmentList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [dataObj, setDataObj] = useState<SurroundingEnvRecord>();
  const [modalVisible, setModalVisible] = useState<boolean>(false); // 标记弹窗是否显示
  const [modalType, setModalType] = useState<'view' | 'edit' | 'add'>('view'); // 弹窗类型
  const [mapData, setMapData] = useState<string>('');

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setModalVisible(false);
    setDataObj(undefined);
    setModalType('view');
    listAction.reload({}); // 刷新列表
  };

  // 弹窗标题
  const modalTitle: string = useMemo(() => {
    if (modalType === 'add') {
      return '新增';
    }
    if (modalType === 'view') {
      return '查看';
    }
    if (modalType === 'edit') {
      return '编辑';
    }
    return '';
  }, [modalType]);

  /**
   * 确认删除
   */
  const confirmDelete: (row: SurroundingEnvRecord) => Promise<void> = async (
    row: SurroundingEnvRecord,
  ): Promise<void> => {
    try {
      const res: ApiResponse<boolean> = await surroundingEnvApi.deleteSurroundingEnv(row.id);
      if (res && res.code === SUCCESS_CODE) {
        message.success('删除成功');
        listAction.reload({});
      }
    } catch {
      // console.error('删除失败:', error);
    }
  };

  /**
   * 删除确认对话框
   */
  const showDeleteDialog: (row: SurroundingEnvRecord) => void = (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除敏感区域&quot;{row.areaName}&quot;吗？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除确认',
      },
      m_props: {
        title: '删除确认',
      },
    });
  };

  /**
   * 处理筛选条件
   */
  const handleFilter: (filter: FilterType) => SurroundingEnvQueryParams = (filter) => {
    const condition: SurroundingEnvQueryParams = {};
    condition.areaName = filter.areaName;
    condition.category = filter.category?.[0]?.code;
    return condition;
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'areaName',
      title: '敏感区域名称',
      width: 150,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'category',
      title: '类别',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A23A05', // 环境类型字典
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list;
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (value: string, record: SurroundingEnvRecord) => {
        return record.categoryText || '-';
      },
    },
    {
      dataIndex: 'userIdText',
      title: '创建人',
      width: 150,
      query: false,
      display: true,
    },
    {
      dataIndex: 'createDate',
      title: '创建时间',
      width: 150,
      query: false,
      display: true,
    },
  ];

  // 构建地图数据
  const enterpriseDetail: (result: SurroundingEnvRecord) => void = (result) => {
    if (result.regionalDescription && result.regionalColor) {
      const datas: Array<{ list: number[]; color: string }> = [];
      const colors: string[] = result.regionalColor.split('&');
      const routers: string[] = result.regionalDescription.split('&');
      routers.forEach((item) => {
        datas.push({ color: colors[datas.length], list: JSON.parse(item) });
      });
      setMapData(JSON.stringify(datas));
    } else {
      setMapData(null);
    }
    setModalVisible(true);
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="surroundingEnvironmentList"
        action={listAction}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        listKey="id"
        operation={[
          {
            element: (
              <div>
                <Button
                  type="primary"
                  onClick={() => {
                    setDataObj(undefined);
                    setModalType('add');
                    setModalVisible(true);
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        request={async (filter: FilterType, pagination) => {
          const resData: ApiResponse<SurroundingEnvRecord[]> =
            await surroundingEnvApi.querySurroundingEnvList({
              aescs: [],
              descs: [],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });
          if (resData.code && resData.code === SUCCESS_CODE) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        rowOperation={(row: SurroundingEnvRecord) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setDataObj(row);
                    setModalType('view');
                    enterpriseDetail(row);
                  }}
                >
                  查看
                </Button>
              ),
            },
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setDataObj(row);
                    setModalType('edit');
                    enterpriseDetail(row);
                  }}
                >
                  编辑
                </Button>
              ),
            },
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  danger
                  onClick={() => {
                    showDeleteDialog(row);
                  }}
                >
                  删除
                </Button>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title={modalTitle}
        width="60%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={modalVisible}
        key="surrounding-environment-modal"
      >
        <SurroundingEnvModal
          mapData={mapData}
          closeModal={closeModal}
          dataObj={dataObj}
          modalType={modalType}
        />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  SurroundingEnvironmentList,
  locales,
  YTHLocalization.getLanguage(),
);
