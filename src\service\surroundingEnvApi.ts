import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  aescs?: string[];
  descs?: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 园区周边环境记录查询参数接口
 */
export interface SurroundingEnvQueryParams {
  /** 敏感区域名称 */
  areaName?: string;
  /** 类别 */
  category?: string;
}

/**
 * 园区周边环境记录数据接口
 */
export interface SurroundingEnvRecord {
  /** 主键 */
  id: string;
  /** 敏感区域名称 */
  areaName: string;
  /** 类别 */
  category: string;
  /** 类别文本 */
  categoryText?: string;
  /** 保护等级 */
  protectionLevel: string;
  /** 保护等级文本 */
  protectionLevelText?: string;
  /** 地理位置描述 */
  locationDescription: string;
  /** 区域描绘 */
  regionalDescription: string;
  /** 区域颜色 */
  regionalColor: string;
  /** 更新人 */
  updateBy: string;
  /** 更新时间 */
  updateDate?: string;
  /** 创建人 */
  userIdText?: string;
  /** 创建时间 */
  createDate?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 园区周边环境编辑参数接口
 */
export interface SurroundingEnvEditParams {
  /** 主键 */
  id?: string;
  /** 敏感区域名称 */
  areaName: string;
  /** 类别 */
  category: string;
  /** 保护等级 */
  protectionLevel: string;
  /** 地理位置描述 */
  locationDescription: string;
  /** 区域描绘 */
  regionalDescription: string;
  /** 区域颜色 */
  regionalColor: string;
}

/**
 * 园区周边环境 API 接口定义
 */
interface SurroundingEnvApiInterface {
  /** 查询园区周边环境记录列表 */
  querySurroundingEnvList: (
    data: PageQueryParams<SurroundingEnvQueryParams>,
  ) => Promise<ApiResponse<SurroundingEnvRecord[]>>;
  /** 新增园区周边环境记录 */
  insertSurroundingEnv: (
    data: SurroundingEnvEditParams,
  ) => Promise<ApiResponse<SurroundingEnvRecord>>;
  /** 更新园区周边环境记录 */
  updateSurroundingEnv: (
    data: SurroundingEnvEditParams,
  ) => Promise<ApiResponse<SurroundingEnvRecord>>;
  /** 删除园区周边环境记录 */
  deleteSurroundingEnv: (id: string) => Promise<ApiResponse<boolean>>;
}

/**
 * 园区周边环境模块 API
 */
const surroundingEnvApi: SurroundingEnvApiInterface = {
  /** 查询园区周边环境记录列表 */
  querySurroundingEnvList: (data) => {
    return envRequest.post('/envSurroundingPark/page', { data });
  },

  /** 新增园区周边环境记录 */
  insertSurroundingEnv: (data) => {
    return envRequest.post('/envSurroundingPark/insert', { data });
  },

  /** 更新园区周边环境记录 */
  updateSurroundingEnv: (data) => {
    return envRequest.put('/envSurroundingPark/update', { data });
  },

  /** 删除园区周边环境记录 */
  deleteSurroundingEnv: (id) => {
    return envRequest.delete(`/envSurroundingPark/removeById?id=${id}`);
  },
};

export default surroundingEnvApi;
