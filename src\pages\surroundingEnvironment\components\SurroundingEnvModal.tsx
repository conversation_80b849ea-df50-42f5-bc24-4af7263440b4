import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, Button } from 'antd';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type {
  ApiResponse,
  SurroundingEnvRecord,
  SurroundingEnvEditParams,
} from '@/service/surroundingEnvApi';
import type { Form } from '@formily/core/esm/models';
import formApi from '@/service/formApi';
import MapDraw from '@/components/common/MapDraw/MapDraw';
import type { MapCardExpose } from '@/components/common/MapCard/components/mapTypes';

/**
 * 园区周边环境弹窗组件的属性类型定义
 */
type PropsTypes = {
  /** 环境数据对象，编辑和查看模式时传入 */
  dataObj?: SurroundingEnvRecord;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
  /** 弹窗模式：查看、编辑、新增 */
  modalType: 'view' | 'edit' | 'add';
  /** 地图数据 */
  mapData: string;
};

/**
 * @description 园区周边环境详情弹窗
 * @returns
 */
const SurroundingEnvModal: React.FC<PropsTypes> = ({
  dataObj,
  closeModal = () => {},
  modalType,
  mapData,
}) => {
  /** 加载状态 */
  const [isLoading, setIsLoading] = useState<boolean>(false);
  /** 表单实例 */
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);
  /** 地图绘制组件的引用 */
  const MapCardMethodRef: React.MutableRefObject<MapCardExpose | null> = React.useRef(null);

  /** 是否为只读模式（查看模式） */
  const isReadOnly: boolean = modalType === 'view';

  /**
   * 初始化表单数据
   * 根据不同的模式设置表单的初始值
   */
  useEffect(() => {
    if (modalType === 'add') {
      // 新增模式，设置默认值
      form.setValues({
        areaName: '',
        category: '',
        protectionLevel: '',
        locationDescription: '',
        regionalDescription: '',
      });
    } else if (dataObj) {
      // 查看或编辑模式，设置现有数据
      form.setValues({
        ...dataObj,
        category: [{ code: dataObj.category, text: dataObj.categoryText }],
      });
    }
  }, [dataObj, modalType, form]);

  /**
   * 处理地图绘制数据，将绘制的线路数据转换为后端需要的格式
   * @param value 地图绘制的线路数据数组
   * @returns 包含区域颜色和区域描述的对象
   */
  const getAllRouter: (value: Array<{ list: number[]; color: string }>) => {
    regionalColor: string;
    regionalDescription: string;
  } = (value) => {
    let colorArr: string = '';
    let routerList: string = '';
    // 遍历获取颜色和坐标数据
    value.forEach((item) => {
      colorArr += `${item.color}&`;
      routerList += `${JSON.stringify(item.list)}&`;
    });
    // 去除最后一个分隔符并返回
    return {
      regionalColor: colorArr.substring(0, colorArr.length - 1),
      regionalDescription: routerList.substring(0, routerList.length - 1),
    };
  };

  /**
   * 保存数据处理函数
   * 验证表单数据并调用相应的API进行新增或更新操作
   */
  const handleSave: () => Promise<void> = async (): Promise<void> => {
    try {
      // 验证表单数据
      await form.validate();
      setIsLoading(true);

      // 获取地图绘制的数据
      const drawData: Array<{ list: number[]; color: string }> =
        MapCardMethodRef.current?.getDrawData();

      // 构建请求参数
      const params: SurroundingEnvEditParams = {
        areaName: form.values?.areaName || '',
        category: form.values?.category?.[0]?.code || '',
        protectionLevel: form.values?.protectionLevel || '',
        locationDescription: form.values?.locationDescription || '',
        ...getAllRouter(drawData),
      };
      let res: ApiResponse<SurroundingEnvRecord>;
      // 根据模式调用不同的API
      if (modalType === 'add') {
        res = await surroundingEnvApi.insertSurroundingEnv(params);
      } else {
        params.id = dataObj?.id;
        res = await surroundingEnvApi.updateSurroundingEnv(params);
      }

      // 处理响应结果
      if (res && res.code === 200) {
        message.success(modalType === 'add' ? '新增成功' : '更新成功');
        closeModal();
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 取消操作处理函数
   * 重置表单并关闭弹窗
   */
  const handleCancel: () => void = () => {
    form.reset();
    closeModal();
  };

  return (
    <div>
      {/* 基本信息表单区域 */}
      <YTHForm form={form} col={2}>
        {/* 敏感区域名称输入框 */}
        <YTHForm.Item
          name="areaName"
          title="敏感区域名称"
          labelType={1}
          componentName="Input"
          componentProps={{
            disabled: isReadOnly,
            placeholder: '请输入',
          }}
          required={!isReadOnly}
        />
        {/* 类别选择器 */}
        <YTHForm.Item
          name="category"
          title="类别"
          labelType={1}
          componentName="Selector"
          componentProps={{
            disabled: isReadOnly,
            request: async () => {
              // 获取环境类型字典数据
              const { list } = await formApi.getDictionary({
                condition: {
                  fatherCode: 'A23A05', // 环境类型字典编码
                },
                currentPage: 0,
                pageSize: 0,
              });
              return list;
            },
            p_props: {
              placeholder: '请选择',
            },
          }}
          required={!isReadOnly}
        />
        {/* 保护等级输入框 */}
        <YTHForm.Item
          name="protectionLevel"
          title="保护等级"
          labelType={1}
          componentName="Input"
          componentProps={{
            disabled: isReadOnly,
            placeholder: '请输入',
          }}
        />
        {/* 地理位置描述文本域 */}
        <YTHForm.Item
          name="locationDescription"
          title="地理位置描述"
          labelType={1}
          componentName="Input"
          componentProps={{
            disabled: isReadOnly,
            placeholder: '请输入',
          }}
        />
        {/* 查看模式下显示更新信息 */}
        {modalType === 'view' && (
          <>
            <YTHForm.Item
              name="userIdText"
              title="创建人"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
            <YTHForm.Item
              name="createDate"
              title="创建时间"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
          </>
        )}
      </YTHForm>

      {/* 地图区域描绘组件 */}
      <div style={{ marginTop: 10 }}>
        <div style={{ marginBottom: 5, fontSize: 14 }}>区域描绘</div>
        <MapDraw
          ref={MapCardMethodRef}
          mapConfig={{ satellite: true }} // 启用卫星地图
          operateType={modalType}
          areaList={mapData}
        />
      </div>

      {/* 底部操作按钮区域 */}
      <div style={{ textAlign: 'right', marginTop: '20px' }}>
        <Button onClick={handleCancel} style={{ marginRight: '8px' }}>
          取消
        </Button>
        {/* 非查看模式下显示保存按钮 */}
        {!isReadOnly && (
          <Button loading={isLoading} type="primary" onClick={handleSave}>
            {modalType === 'add' ? '新增' : '保存'}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SurroundingEnvModal;
